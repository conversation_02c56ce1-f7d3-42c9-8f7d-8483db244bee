// Script untuk import sample data ke PocketBase
// Jalankan di browser console atau Node.js dengan PocketBase SDK

const POCKETBASE_URL = 'http://**************:8080';

// Sample data dari file sample_data.json
const sampleData = {
  users: [
    {
      username: "farmer1",
      email: "<EMAIL>", 
      password: "farmer123",
      passwordConfirm: "farmer123",
      name: "<PERSON><PERSON>",
      emailVisibility: false
    },
    {
      username: "admin",
      email: "<EMAIL>",
      password: "admin123", 
      passwordConfirm: "admin123",
      name: "Admin Smart Farm",
      emailVisibility: false
    }
  ],
  devices: [
    {
      deviceName: "Smart Farm Greenhouse A",
      deviceId: "device_123",
      location: "Greenhouse A - Sektor 1", 
      isActive: true,
      userId: "USER_ID_1" // Will be replaced with actual user ID
    },
    {
      deviceName: "Smart Farm Outdoor B",
      deviceId: "device_456", 
      location: "Outdoor Farm - Sektor 2",
      isActive: true,
      userId: "USER_ID_1"
    }
  ],
  sensor_data: [
    {
      deviceId: "device_123",
      temperature: 28.5,
      humidity: 65.2,
      soilMoisture: 45.8,
      lightIntensity: 850.0,
      ph: 6.8
    },
    {
      deviceId: "device_123", 
      temperature: 29.1,
      humidity: 63.7,
      soilMoisture: 42.3,
      lightIntensity: 920.0,
      ph: 6.9
    },
    {
      deviceId: "device_123",
      temperature: 30.2,
      humidity: 61.5, 
      soilMoisture: 38.9,
      lightIntensity: 1050.0,
      ph: 7.0
    },
    {
      deviceId: "device_456",
      temperature: 26.8,
      humidity: 70.1,
      soilMoisture: 55.2,
      lightIntensity: 780.0,
      ph: 6.5
    }
  ],
  controls: [
    {
      deviceId: "device_123",
      namaKontrol: "Sprinkler System",
      idKontrol: 1,
      isON: false,
      automated: true,
      parameter: "Auto mode - Soil moisture < 40%"
    },
    {
      deviceId: "device_123",
      namaKontrol: "Kipas Angin", 
      idKontrol: 2,
      isON: true,
      automated: false,
      parameter: "Manual mode - Speed: Medium"
    },
    {
      deviceId: "device_123",
      namaKontrol: "Drip Irrigation",
      idKontrol: 3,
      isON: false,
      automated: true,
      parameter: "Schedule mode - 06:00 & 18:00"
    },
    {
      deviceId: "device_123",
      namaKontrol: "Mist System",
      idKontrol: 4,
      isON: false,
      automated: true,
      parameter: "Auto mode - Humidity < 60%"
    },
    {
      deviceId: "device_123",
      namaKontrol: "Water Valve",
      idKontrol: 5,
      isON: false,
      automated: false,
      parameter: "Manual control"
    },
    {
      deviceId: "device_123",
      namaKontrol: "Water Pump",
      idKontrol: 6,
      isON: false,
      automated: true,
      parameter: "Auto mode - Tank level < 20%"
    }
  ],
  schedules: [
    {
      name: "Penyiraman Pagi",
      description: "Penyiraman otomatis setiap pagi untuk menjaga kelembaban tanah",
      deviceId: "device_123",
      controlId: "sprinkler",
      type: "time_based",
      startTime: "2024-01-15T06:00:00.000Z",
      endTime: "2024-01-15T06:30:00.000Z", 
      daysOfWeek: [1, 2, 3, 4, 5],
      isActive: true,
      isRepeating: true,
      parameters: {
        duration: 30,
        intensity: "medium",
        flow_rate: "5L/min"
      }
    },
    {
      name: "Penyiraman Sore",
      description: "Penyiraman sore hari untuk tanaman yang membutuhkan air ekstra",
      deviceId: "device_123",
      controlId: "drip", 
      type: "time_based",
      startTime: "2024-01-15T17:00:00.000Z",
      endTime: "2024-01-15T17:45:00.000Z",
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
      isActive: true,
      isRepeating: true,
      parameters: {
        duration: 45,
        intensity: "low",
        flow_rate: "2L/min"
      }
    },
    {
      name: "Ventilasi Siang",
      description: "Kipas angin otomatis saat cuaca panas untuk sirkulasi udara",
      deviceId: "device_123",
      controlId: "kipas angin",
      type: "time_based",
      startTime: "2024-01-15T11:00:00.000Z",
      endTime: "2024-01-15T15:00:00.000Z",
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
      isActive: true,
      isRepeating: true,
      parameters: {
        duration: 240,
        speed: "medium", 
        auto_adjust: true
      }
    },
    {
      name: "Misting Pagi",
      description: "Sistem kabut untuk meningkatkan kelembaban udara pagi hari",
      deviceId: "device_123",
      controlId: "mist",
      type: "time_based",
      startTime: "2024-01-15T07:00:00.000Z",
      endTime: "2024-01-15T07:15:00.000Z",
      daysOfWeek: [1, 3, 5],
      isActive: false,
      isRepeating: true,
      parameters: {
        duration: 15,
        intensity: "fine",
        humidity_target: 70
      }
    }
  ],
  automation_rules: [
    {
      name: "Auto Sprinkler - Tanah Kering",
      description: "Nyalakan sprinkler otomatis jika kelembaban tanah di bawah 35%",
      deviceId: "device_123",
      controlId: "sprinkler",
      conditions: [
        {
          sensor: "soilMoisture",
          operator: "<",
          value: 35,
          unit: "percent"
        }
      ],
      actions: [
        {
          type: "turn_on",
          controlId: "sprinkler",
          parameters: {
            duration: 20,
            intensity: "medium"
          }
        }
      ],
      isActive: true,
      logic: "AND"
    },
    {
      name: "Auto Kipas - Suhu Tinggi",
      description: "Nyalakan kipas angin jika suhu lebih dari 32°C",
      deviceId: "device_123",
      controlId: "kipas angin",
      conditions: [
        {
          sensor: "temperature",
          operator: ">",
          value: 32,
          unit: "celsius"
        }
      ],
      actions: [
        {
          type: "turn_on",
          controlId: "kipas angin",
          parameters: {
            speed: "high",
            duration: 60
          }
        }
      ],
      isActive: true,
      logic: "AND"
    },
    {
      name: "Auto Mist - Kelembaban Rendah",
      description: "Nyalakan mist system jika kelembaban udara di bawah 55%",
      deviceId: "device_123",
      controlId: "mist",
      conditions: [
        {
          sensor: "humidity",
          operator: "<",
          value: 55,
          unit: "percent"
        },
        {
          sensor: "temperature",
          operator: ">",
          value: 28,
          unit: "celsius"
        }
      ],
      actions: [
        {
          type: "turn_on",
          controlId: "mist",
          parameters: {
            duration: 10,
            intensity: "fine"
          }
        }
      ],
      isActive: false,
      logic: "AND"
    }
  ]
};

// Function untuk import data
async function importSampleData() {
  console.log('🚀 Starting sample data import...');
  
  try {
    // 1. Import Users (create accounts)
    console.log('📝 Creating user accounts...');
    const createdUsers = [];
    for (const user of sampleData.users) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/users/records`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(user)
        });
        
        if (response.ok) {
          const createdUser = await response.json();
          createdUsers.push(createdUser);
          console.log(`✅ User created: ${user.username}`);
        } else {
          console.log(`⚠️ User ${user.username} might already exist`);
        }
      } catch (error) {
        console.log(`❌ Error creating user ${user.username}:`, error);
      }
    }

    // Get admin token for other operations
    console.log('🔑 Getting admin authentication...');
    const authResponse = await fetch(`${POCKETBASE_URL}/api/admins/auth-with-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identity: '<EMAIL>', // Replace with your admin email
        password: 'admin123' // Replace with your admin password
      })
    });

    let authToken = '';
    if (authResponse.ok) {
      const authData = await authResponse.json();
      authToken = authData.token;
      console.log('✅ Admin authenticated');
    } else {
      console.log('⚠️ Admin auth failed, continuing without auth...');
    }

    const headers = {
      'Content-Type': 'application/json',
    };
    
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // 2. Import Devices
    console.log('🏭 Creating devices...');
    if (createdUsers.length > 0) {
      sampleData.devices.forEach(device => {
        device.userId = createdUsers[0].id; // Assign to first user
      });
    }
    
    for (const device of sampleData.devices) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/devices/records`, {
          method: 'POST',
          headers,
          body: JSON.stringify(device)
        });
        
        if (response.ok) {
          console.log(`✅ Device created: ${device.deviceName}`);
        }
      } catch (error) {
        console.log(`❌ Error creating device:`, error);
      }
    }

    // 3. Import Sensor Data
    console.log('📊 Creating sensor data...');
    for (const sensorData of sampleData.sensor_data) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/sensor_data/records`, {
          method: 'POST',
          headers,
          body: JSON.stringify(sensorData)
        });
        
        if (response.ok) {
          console.log(`✅ Sensor data created for device: ${sensorData.deviceId}`);
        }
      } catch (error) {
        console.log(`❌ Error creating sensor data:`, error);
      }
    }

    // 4. Import Controls
    console.log('🎛️ Creating controls...');
    for (const control of sampleData.controls) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/controls/records`, {
          method: 'POST',
          headers,
          body: JSON.stringify(control)
        });
        
        if (response.ok) {
          console.log(`✅ Control created: ${control.namaKontrol}`);
        }
      } catch (error) {
        console.log(`❌ Error creating control:`, error);
      }
    }

    // 5. Import Schedules
    console.log('⏰ Creating schedules...');
    for (const schedule of sampleData.schedules) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/schedules/records`, {
          method: 'POST',
          headers,
          body: JSON.stringify(schedule)
        });
        
        if (response.ok) {
          console.log(`✅ Schedule created: ${schedule.name}`);
        }
      } catch (error) {
        console.log(`❌ Error creating schedule:`, error);
      }
    }

    // 6. Import Automation Rules
    console.log('🤖 Creating automation rules...');
    for (const rule of sampleData.automation_rules) {
      try {
        const response = await fetch(`${POCKETBASE_URL}/api/collections/automation_rules/records`, {
          method: 'POST',
          headers,
          body: JSON.stringify(rule)
        });
        
        if (response.ok) {
          console.log(`✅ Automation rule created: ${rule.name}`);
        }
      } catch (error) {
        console.log(`❌ Error creating automation rule:`, error);
      }
    }

    console.log('🎉 Sample data import completed!');
    console.log('📱 You can now test the Flutter app with sample data');
    
  } catch (error) {
    console.error('❌ Import failed:', error);
  }
}

// Instructions for usage
console.log(`
🔧 CARA MENGGUNAKAN SCRIPT INI:

1. Buka browser dan akses: ${POCKETBASE_URL}/_/
2. Buka Developer Tools (F12) 
3. Pergi ke tab Console
4. Copy-paste script ini ke console
5. Jalankan: importSampleData()

ATAU

1. Simpan script ini sebagai file .js
2. Install Node.js dan PocketBase SDK
3. Jalankan dengan: node import_sample_data.js

⚠️ PENTING: 
- Pastikan collections sudah dibuat terlebih dahulu
- Ganti admin email/password sesuai dengan setup Anda
- Script ini akan membuat data sample untuk testing
`);

// Export untuk Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { importSampleData, sampleData };
}
