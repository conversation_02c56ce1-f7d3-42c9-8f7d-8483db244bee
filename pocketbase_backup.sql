-- PocketBase Collections Backup SQL
-- Alternative setup jika import JSON gagal
-- Jalankan query ini di PocketBase SQLite database

-- =====================================================
-- Table: schedules
-- =====================================================
CREATE TABLE IF NOT EXISTS schedules (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(8))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    created TEXT DEFAULT (datetime('now')),
    updated TEXT DEFAULT (datetime('now')),
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 255),
    description TEXT CHECK (length(description) <= 1000),
    deviceId TEXT NOT NULL CHECK (length(deviceId) >= 1 AND length(deviceId) <= 255),
    controlId TEXT NOT NULL CHECK (length(controlId) >= 1 AND length(controlId) <= 255),
    type TEXT NOT NULL CHECK (type IN ('time_based', 'sensor_based', 'manual')),
    startTime TEXT NOT NULL,
    endTime TEXT,
    daysOfWeek TEXT, -- JSON array
    isActive INTEGER DEFAULT 0 CHECK (isActive IN (0, 1)),
    isRepeating INTEGER DEFAULT 0 CHECK (isRepeating IN (0, 1)),
    parameters TEXT -- JSON object
);

-- Indexes untuk schedules
CREATE INDEX IF NOT EXISTS idx_schedules_device ON schedules (deviceId);
CREATE INDEX IF NOT EXISTS idx_schedules_active ON schedules (isActive);
CREATE INDEX IF NOT EXISTS idx_schedules_type ON schedules (type);
CREATE INDEX IF NOT EXISTS idx_schedules_created ON schedules (created);

-- =====================================================
-- Table: automation_rules  
-- =====================================================
CREATE TABLE IF NOT EXISTS automation_rules (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(8))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    created TEXT DEFAULT (datetime('now')),
    updated TEXT DEFAULT (datetime('now')),
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 255),
    description TEXT CHECK (length(description) <= 1000),
    deviceId TEXT NOT NULL CHECK (length(deviceId) >= 1 AND length(deviceId) <= 255),
    controlId TEXT NOT NULL CHECK (length(controlId) >= 1 AND length(controlId) <= 255),
    conditions TEXT NOT NULL, -- JSON array
    actions TEXT NOT NULL, -- JSON array
    isActive INTEGER DEFAULT 0 CHECK (isActive IN (0, 1)),
    logic TEXT NOT NULL CHECK (logic IN ('AND', 'OR'))
);

-- Indexes untuk automation_rules
CREATE INDEX IF NOT EXISTS idx_automation_device ON automation_rules (deviceId);
CREATE INDEX IF NOT EXISTS idx_automation_active ON automation_rules (isActive);
CREATE INDEX IF NOT EXISTS idx_automation_created ON automation_rules (created);

-- =====================================================
-- Table: schedule_executions
-- =====================================================
CREATE TABLE IF NOT EXISTS schedule_executions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(8))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    created TEXT DEFAULT (datetime('now')),
    updated TEXT DEFAULT (datetime('now')),
    scheduleId TEXT NOT NULL CHECK (length(scheduleId) >= 1 AND length(scheduleId) <= 255),
    executedAt TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('success', 'failed', 'skipped')),
    result TEXT CHECK (length(result) <= 2000),
    error TEXT CHECK (length(error) <= 2000)
);

-- Indexes untuk schedule_executions
CREATE INDEX IF NOT EXISTS idx_executions_schedule ON schedule_executions (scheduleId);
CREATE INDEX IF NOT EXISTS idx_executions_status ON schedule_executions (status);
CREATE INDEX IF NOT EXISTS idx_executions_date ON schedule_executions (executedAt);
CREATE INDEX IF NOT EXISTS idx_executions_created ON schedule_executions (created);

-- =====================================================
-- Sample Data untuk Testing
-- =====================================================

-- Sample Schedule: Penyiraman Pagi
INSERT OR IGNORE INTO schedules (
    id, name, description, deviceId, controlId, type, 
    startTime, endTime, daysOfWeek, isActive, isRepeating, parameters
) VALUES (
    'schedule_001',
    'Penyiraman Pagi',
    'Penyiraman otomatis setiap pagi jam 6:00',
    'device_123',
    'sprinkler',
    'time_based',
    '2024-01-01T06:00:00Z',
    '2024-01-01T06:30:00Z',
    '[1,2,3,4,5]',
    1,
    1,
    '{"duration": 30, "intensity": "medium"}'
);

-- Sample Schedule: Kipas Sore
INSERT OR IGNORE INTO schedules (
    id, name, description, deviceId, controlId, type,
    startTime, endTime, daysOfWeek, isActive, isRepeating, parameters
) VALUES (
    'schedule_002', 
    'Kipas Sore',
    'Kipas angin otomatis sore hari',
    'device_123',
    'kipas angin',
    'time_based',
    '2024-01-01T15:00:00Z',
    '2024-01-01T17:00:00Z',
    '[0,1,2,3,4,5,6]',
    1,
    1,
    '{"speed": "medium", "duration": 120}'
);

-- Sample Automation Rule: Suhu Tinggi
INSERT OR IGNORE INTO automation_rules (
    id, name, description, deviceId, controlId, 
    conditions, actions, isActive, logic
) VALUES (
    'rule_001',
    'Auto Kipas Suhu Tinggi',
    'Nyalakan kipas jika suhu lebih dari 35°C',
    'device_123',
    'kipas angin',
    '[{"sensor": "temperature", "operator": ">", "value": 35, "unit": "celsius"}]',
    '[{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "high"}}]',
    1,
    'AND'
);

-- Sample Automation Rule: Kelembaban Rendah
INSERT OR IGNORE INTO automation_rules (
    id, name, description, deviceId, controlId,
    conditions, actions, isActive, logic
) VALUES (
    'rule_002',
    'Auto Sprinkler Kering',
    'Nyalakan sprinkler jika kelembaban tanah < 30%',
    'device_123', 
    'sprinkler',
    '[{"sensor": "soil_moisture", "operator": "<", "value": 30, "unit": "percent"}]',
    '[{"type": "turn_on", "controlId": "sprinkler", "parameters": {"duration": 15}}]',
    1,
    'AND'
);

-- Sample Schedule Execution Log
INSERT OR IGNORE INTO schedule_executions (
    id, scheduleId, executedAt, status, result
) VALUES (
    'exec_001',
    'schedule_001',
    '2024-01-01T06:00:00Z',
    'success',
    'Sprinkler activated for 30 minutes'
);

-- =====================================================
-- Triggers untuk auto-update timestamp
-- =====================================================

-- Trigger untuk schedules
CREATE TRIGGER IF NOT EXISTS schedules_updated_trigger
    AFTER UPDATE ON schedules
    FOR EACH ROW
BEGIN
    UPDATE schedules SET updated = datetime('now') WHERE id = NEW.id;
END;

-- Trigger untuk automation_rules
CREATE TRIGGER IF NOT EXISTS automation_rules_updated_trigger
    AFTER UPDATE ON automation_rules
    FOR EACH ROW
BEGIN
    UPDATE automation_rules SET updated = datetime('now') WHERE id = NEW.id;
END;

-- Trigger untuk schedule_executions
CREATE TRIGGER IF NOT EXISTS schedule_executions_updated_trigger
    AFTER UPDATE ON schedule_executions
    FOR EACH ROW
BEGIN
    UPDATE schedule_executions SET updated = datetime('now') WHERE id = NEW.id;
END;

-- =====================================================
-- Views untuk kemudahan query
-- =====================================================

-- View: Active Schedules
CREATE VIEW IF NOT EXISTS active_schedules AS
SELECT 
    s.*,
    CASE 
        WHEN s.type = 'time_based' THEN 'Jadwal Waktu'
        WHEN s.type = 'sensor_based' THEN 'Jadwal Sensor'
        ELSE 'Manual'
    END as type_label
FROM schedules s
WHERE s.isActive = 1
ORDER BY s.created DESC;

-- View: Recent Executions
CREATE VIEW IF NOT EXISTS recent_executions AS
SELECT 
    se.*,
    s.name as schedule_name,
    s.controlId
FROM schedule_executions se
JOIN schedules s ON se.scheduleId = s.id
ORDER BY se.executedAt DESC
LIMIT 50;

-- =====================================================
-- Cleanup old execution logs (optional)
-- =====================================================

-- Delete execution logs older than 30 days
-- DELETE FROM schedule_executions 
-- WHERE created < datetime('now', '-30 days');

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check if tables created successfully
-- SELECT name FROM sqlite_master WHERE type='table' AND name IN ('schedules', 'automation_rules', 'schedule_executions');

-- Count records in each table
-- SELECT 'schedules' as table_name, COUNT(*) as count FROM schedules
-- UNION ALL
-- SELECT 'automation_rules', COUNT(*) FROM automation_rules  
-- UNION ALL
-- SELECT 'schedule_executions', COUNT(*) FROM schedule_executions;
