# 🚨 PocketBase Collections Recovery Guide

## Masalah yang Te<PERSON><PERSON>di
<PERSON>ka <PERSON><PERSON> **Import collections** di PocketBase, semua collections lama terhapus dan diganti dengan yang baru. Collections yang hilang:
- `users` (authentication)
- `devices` (perangkat IoT)
- `sensor_data` (data sensor)
- `controls` (kontrol alat)

## 🛠️ Solusi Recovery

### **Opsi 1: Import Complete Collections (RECOMMENDED)**

File `pocketbase_complete_collections.json` berisi **SEMUA collections** (lama + baru):

#### Collections Lama (Restored):
- ✅ `users` - Authentication users
- ✅ `devices` - Device management  
- ✅ `sensor_data` - Sensor readings
- ✅ `controls` - Device controls

#### Collections Baru (Added):
- ✅ `schedules` - Scheduling system
- ✅ `automation_rules` - Automation rules
- ✅ `schedule_executions` - Execution logs

#### Cara Import:
1. Buka PocketBase Admin: `http://**************:8080/_/`
2. Login sebagai admin
3. **Settings** → **Import collections**
4. Copy-paste isi file `pocketbase_complete_collections.json`
5. Klik **Import**

### **Opsi 2: Manual Creation (Jika Import Gagal)**

Buat collections satu per satu di PocketBase Admin:

#### 1. Collection: `users` (Auth Collection)
```
Type: Auth Collection
Fields:
- name (text, optional)
- avatar (file, optional, images only)

Settings:
- Allow email auth: Yes
- Allow username auth: Yes
- Min password length: 8
```

#### 2. Collection: `devices`
```
Type: Base Collection
Fields:
- deviceName (text, required, max: 255)
- deviceId (text, required, unique, max: 255)
- location (text, optional, max: 500)
- isActive (bool, default: false)
- userId (relation to users, required)

API Rules: @request.auth.id != ""
```

#### 3. Collection: `sensor_data`
```
Type: Base Collection
Fields:
- deviceId (text, required, max: 255)
- temperature (number, optional)
- humidity (number, optional)
- soilMoisture (number, optional)
- lightIntensity (number, optional)
- ph (number, optional)

API Rules: @request.auth.id != ""
```

#### 4. Collection: `controls`
```
Type: Base Collection
Fields:
- deviceId (text, required, max: 255)
- namaKontrol (text, required, max: 255)
- idKontrol (number, required)
- isON (bool, default: false)
- automated (bool, default: false)
- parameter (text, optional, max: 500)

API Rules: @request.auth.id != ""
```

#### 5. Collection: `schedules`
```
Type: Base Collection
Fields:
- name (text, required, max: 255)
- description (text, optional, max: 1000)
- deviceId (text, required, max: 255)
- controlId (text, required, max: 255)
- type (select: time_based, sensor_based, manual)
- startTime (date, required)
- endTime (date, optional)
- daysOfWeek (json, optional)
- isActive (bool, default: false)
- isRepeating (bool, default: false)
- parameters (json, optional)

API Rules: @request.auth.id != ""
```

#### 6. Collection: `automation_rules`
```
Type: Base Collection
Fields:
- name (text, required, max: 255)
- description (text, optional, max: 1000)
- deviceId (text, required, max: 255)
- controlId (text, required, max: 255)
- conditions (json, required)
- actions (json, required)
- isActive (bool, default: false)
- logic (select: AND, OR)

API Rules: @request.auth.id != ""
```

#### 7. Collection: `schedule_executions`
```
Type: Base Collection
Fields:
- scheduleId (text, required, max: 255)
- executedAt (date, required)
- status (select: success, failed, skipped)
- result (text, optional, max: 2000)
- error (text, optional, max: 2000)

API Rules: @request.auth.id != ""
```

## 🔍 Verifikasi Recovery

Setelah recovery, pastikan:

### 1. Check Collections
Di PocketBase Admin → Collections, pastikan ada:
- [x] users
- [x] devices  
- [x] sensor_data
- [x] controls
- [x] schedules
- [x] automation_rules
- [x] schedule_executions

### 2. Test API Endpoints
```bash
# Test existing endpoints
curl "http://**************:8080/api/collections/users/records"
curl "http://**************:8080/api/collections/devices/records"
curl "http://**************:8080/api/collections/sensor_data/records"
curl "http://**************:8080/api/collections/controls/records"

# Test new endpoints
curl "http://**************:8080/api/collections/schedules/records"
curl "http://**************:8080/api/collections/automation_rules/records"
curl "http://**************:8080/api/collections/schedule_executions/records"
```

### 3. Test Flutter App
- Login masih berfungsi
- Sensor dashboard menampilkan data
- Control devices berfungsi
- Scheduling screen terbuka tanpa error

## 📱 Update Flutter Code

Setelah recovery, tambahkan URL endpoints baru ke `lib/common/url.dart`:

```dart
class UrlData {
  // Existing URLs (tetap sama)
  final String url_users = 'http://**************:8080/api/collections/users';
  final String url_devices = 'http://**************:8080/api/collections/devices';
  final String url_sensor_data = 'http://**************:8080/api/collections/sensor_data';
  final String url_controls = 'http://**************:8080/api/collections/controls';
  
  // New URLs for scheduling
  final String url_schedules = 'http://**************:8080/api/collections/schedules';
  final String url_automation_rules = 'http://**************:8080/api/collections/automation_rules';
  final String url_schedule_executions = 'http://**************:8080/api/collections/schedule_executions';
}
```

## 🚨 Pencegahan di Masa Depan

### 1. Backup Database
Sebelum import collections, selalu backup:
```bash
# Backup PocketBase database
cp pb_data/data.db pb_data/data_backup_$(date +%Y%m%d).db
```

### 2. Export Collections
Export collections yang sudah ada sebelum import baru:
- PocketBase Admin → Settings → Export collections
- Simpan file JSON sebagai backup

### 3. Test di Development
- Setup PocketBase development server terpisah
- Test import di development dulu
- Baru apply ke production

## 📞 Troubleshooting

### Error: "Collection already exists"
- Hapus collection yang conflict
- Import ulang

### Error: "Invalid field type"  
- Pastikan PocketBase versi terbaru
- Check syntax JSON

### App masih error setelah recovery
- Clear app cache/data
- Restart PocketBase server
- Check authentication token

---

**⚠️ PENTING**: Selalu backup database sebelum melakukan perubahan besar pada collections!
