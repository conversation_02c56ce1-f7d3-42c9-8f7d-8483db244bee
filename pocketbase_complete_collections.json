{"collections": [{"id": "users", "name": "users", "type": "auth", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "avatar", "name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "protected": false}}], "indexes": [], "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": null, "minPasswordLength": 8, "onlyEmailDomains": null, "requireEmail": false}}, {"id": "devices", "name": "devices", "type": "base", "system": false, "schema": [{"id": "deviceName", "name": "deviceName", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": true, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "location", "name": "location", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "userId", "name": "userId", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "users", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": []}}], "indexes": ["CREATE INDEX idx_devices_user ON devices (userId)", "CREATE INDEX idx_devices_active ON devices (isActive)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "sensor_data", "name": "sensor_data", "type": "base", "system": false, "schema": [{"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "temperature", "name": "temperature", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "humidity", "name": "humidity", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "soilMoisture", "name": "soilMoisture", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "lightIntensity", "name": "lightIntensity", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "ph", "name": "ph", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}], "indexes": ["CREATE INDEX idx_sensor_device ON sensor_data (deviceId)", "CREATE INDEX idx_sensor_created ON sensor_data (created)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "controls", "name": "controls", "type": "base", "system": false, "schema": [{"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "namaKontrol", "name": "namaKontrol", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "idKontrol", "name": "idKontrol", "type": "number", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null}}, {"id": "isON", "name": "isON", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "automated", "name": "automated", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "parameter", "name": "parameter", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}], "indexes": ["CREATE INDEX idx_controls_device ON controls (deviceId)", "CREATE INDEX idx_controls_id ON controls (idKontrol)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "schedules", "name": "schedules", "type": "base", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "controlId", "name": "controlId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "type", "name": "type", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["time_based", "sensor_based", "manual"]}}, {"id": "startTime", "name": "startTime", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "endTime", "name": "endTime", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"id": "daysOfWeek", "name": "daysOfWeek", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "isRepeating", "name": "isRepeating", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "parameters", "name": "parameters", "type": "json", "system": false, "required": false, "unique": false, "options": {}}], "indexes": ["CREATE INDEX idx_schedules_device ON schedules (deviceId)", "CREATE INDEX idx_schedules_active ON schedules (isActive)", "CREATE INDEX idx_schedules_type ON schedules (type)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "automation_rules", "name": "automation_rules", "type": "base", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"id": "deviceId", "name": "deviceId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "controlId", "name": "controlId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "conditions", "name": "conditions", "type": "json", "system": false, "required": true, "unique": false, "options": {}}, {"id": "actions", "name": "actions", "type": "json", "system": false, "required": true, "unique": false, "options": {}}, {"id": "isActive", "name": "isActive", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "logic", "name": "logic", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["AND", "OR"]}}], "indexes": ["CREATE INDEX idx_automation_device ON automation_rules (deviceId)", "CREATE INDEX idx_automation_active ON automation_rules (isActive)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "schedule_executions", "name": "schedule_executions", "type": "base", "system": false, "schema": [{"id": "scheduleId", "name": "scheduleId", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"id": "executedAt", "name": "executedAt", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["success", "failed", "skipped"]}}, {"id": "result", "name": "result", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}, {"id": "error", "name": "error", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}], "indexes": ["CREATE INDEX idx_executions_schedule ON schedule_executions (scheduleId)", "CREATE INDEX idx_executions_status ON schedule_executions (status)", "CREATE INDEX idx_executions_date ON schedule_executions (executedAt)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}], "data": {"users": [{"username": "farmer1", "email": "<EMAIL>", "password": "farmer123", "passwordConfirm": "farmer123", "name": "<PERSON><PERSON>", "emailVisibility": false}, {"username": "admin", "email": "<EMAIL>", "password": "admin123", "passwordConfirm": "admin123", "name": "Admin Smart Farm", "emailVisibility": false}], "devices": [{"deviceName": "Smart Farm Greenhouse A", "deviceId": "device_123", "location": "Greenhouse A - Sektor 1", "isActive": true}, {"deviceName": "Smart Farm Outdoor B", "deviceId": "device_456", "location": "Outdoor Farm - Sektor 2", "isActive": true}], "sensor_data": [{"deviceId": "device_123", "temperature": 28.5, "humidity": 65.2, "soilMoisture": 45.8, "lightIntensity": 850.0, "ph": 6.8}, {"deviceId": "device_123", "temperature": 29.1, "humidity": 63.7, "soilMoisture": 42.3, "lightIntensity": 920.0, "ph": 6.9}, {"deviceId": "device_123", "temperature": 30.2, "humidity": 61.5, "soilMoisture": 38.9, "lightIntensity": 1050.0, "ph": 7.0}, {"deviceId": "device_456", "temperature": 26.8, "humidity": 70.1, "soilMoisture": 55.2, "lightIntensity": 780.0, "ph": 6.5}], "controls": [{"deviceId": "device_123", "namaKontrol": "Sprinkler System", "idKontrol": 1, "isON": false, "automated": true, "parameter": "Auto mode - Soil moisture < 40%"}, {"deviceId": "device_123", "namaKontrol": "<PERSON><PERSON>", "idKontrol": 2, "isON": true, "automated": false, "parameter": "Manual mode - Speed: Medium"}, {"deviceId": "device_123", "namaKontrol": "Drip Irrigation", "idKontrol": 3, "isON": false, "automated": true, "parameter": "Schedule mode - 06:00 & 18:00"}, {"deviceId": "device_123", "namaKontrol": "Mist System", "idKontrol": 4, "isON": false, "automated": true, "parameter": "Auto mode - Humidity < 60%"}, {"deviceId": "device_123", "namaKontrol": "Water Valve", "idKontrol": 5, "isON": false, "automated": false, "parameter": "Manual control"}, {"deviceId": "device_123", "namaKontrol": "Water Pump", "idKontrol": 6, "isON": false, "automated": true, "parameter": "Auto mode - Tank level < 20%"}], "schedules": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>raman otomatis setiap pagi untuk menjaga kelembaban tanah", "deviceId": "device_123", "controlId": "sprinkler", "type": "time_based", "startTime": "2024-01-15T06:00:00.000Z", "endTime": "2024-01-15T06:30:00.000Z", "daysOfWeek": [1, 2, 3, 4, 5], "isActive": true, "isRepeating": true, "parameters": {"duration": 30, "intensity": "medium", "flow_rate": "5L/min"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Penyiraman sore hari untuk tanaman yang membutuhkan air ekstra", "deviceId": "device_123", "controlId": "drip", "type": "time_based", "startTime": "2024-01-15T17:00:00.000Z", "endTime": "2024-01-15T17:45:00.000Z", "daysOfWeek": [0, 1, 2, 3, 4, 5, 6], "isActive": true, "isRepeating": true, "parameters": {"duration": 45, "intensity": "low", "flow_rate": "2L/min"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> angin otomatis saat cuaca panas untuk sirkulasi udara", "deviceId": "device_123", "controlId": "kipas angin", "type": "time_based", "startTime": "2024-01-15T11:00:00.000Z", "endTime": "2024-01-15T15:00:00.000Z", "daysOfWeek": [0, 1, 2, 3, 4, 5, 6], "isActive": true, "isRepeating": true, "parameters": {"duration": 240, "speed": "medium", "auto_adjust": true}}, {"name": "<PERSON><PERSON>", "description": "Sistem kabut untuk meningkatkan kelembaban udara pagi hari", "deviceId": "device_123", "controlId": "mist", "type": "time_based", "startTime": "2024-01-15T07:00:00.000Z", "endTime": "2024-01-15T07:15:00.000Z", "daysOfWeek": [1, 3, 5], "isActive": false, "isRepeating": true, "parameters": {"duration": 15, "intensity": "fine", "humidity_target": 70}}], "automation_rules": [{"name": "Auto Sprinkler - <PERSON><PERSON>", "description": "Nyalakan sprinkler otomatis jika kelembaban tanah di bawah 35%", "deviceId": "device_123", "controlId": "sprinkler", "conditions": [{"sensor": "soilMoisture", "operator": "<", "value": 35, "unit": "percent"}], "actions": [{"type": "turn_on", "controlId": "sprinkler", "parameters": {"duration": 20, "intensity": "medium"}}], "isActive": true, "logic": "AND"}, {"name": "Auto Kipas - <PERSON><PERSON>", "description": "<PERSON>yal<PERSON><PERSON> kipas angin jika suhu lebih dari 32°C", "deviceId": "device_123", "controlId": "kipas angin", "conditions": [{"sensor": "temperature", "operator": ">", "value": 32, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "high", "duration": 60}}], "isActive": true, "logic": "AND"}, {"name": "Auto Mist - Kelembaban Rendah", "description": "Nyalakan mist system jika kelembaban udara di bawah 55%", "deviceId": "device_123", "controlId": "mist", "conditions": [{"sensor": "humidity", "operator": "<", "value": 55, "unit": "percent"}, {"sensor": "temperature", "operator": ">", "value": 28, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "mist", "parameters": {"duration": 10, "intensity": "fine"}}], "isActive": false, "logic": "AND"}], "schedule_executions": [{"scheduleId": "schedule_001", "executedAt": "2024-01-15T06:00:00.000Z", "status": "success", "result": "Sprinkler activated successfully for 30 minutes. Soil moisture increased from 38% to 52%", "error": ""}, {"scheduleId": "schedule_002", "executedAt": "2024-01-14T17:00:00.000Z", "status": "success", "result": "Drip irrigation completed. Duration: 45 minutes. Water usage: 90L", "error": ""}, {"scheduleId": "schedule_003", "executedAt": "2024-01-14T11:00:00.000Z", "status": "success", "result": "Ventilation system ran for 4 hours. Temperature maintained at 29-31°C", "error": ""}, {"scheduleId": "schedule_001", "executedAt": "2024-01-14T06:00:00.000Z", "status": "failed", "result": "", "error": "Water pump malfunction detected. Unable to start sprinkler system"}, {"scheduleId": "schedule_004", "executedAt": "2024-01-13T07:00:00.000Z", "status": "skipped", "result": "Schedule skipped - humidity already at target level (72%)", "error": ""}]}}