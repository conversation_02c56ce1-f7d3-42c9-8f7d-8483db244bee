// PocketBase Collections Setup Script
// Run this in PocketBase Admin UI -> Settings -> Import collections
// Or use PocketBase JavaScript SDK to create collections programmatically

// Collection 1: schedules
const schedulesCollection = {
  "id": "schedules",
  "name": "schedules", 
  "type": "base",
  "system": false,
  "schema": [
    {
      "id": "name",
      "name": "name",
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "description", 
      "name": "description",
      "type": "text",
      "system": false,
      "required": false,
      "unique": false,
      "options": {
        "min": null,
        "max": 1000,
        "pattern": ""
      }
    },
    {
      "id": "deviceId",
      "name": "deviceId", 
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "controlId",
      "name": "controlId",
      "type": "text", 
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "type",
      "name": "type",
      "type": "select",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "maxSelect": 1,
        "values": [
          "time_based",
          "sensor_based", 
          "manual"
        ]
      }
    },
    {
      "id": "startTime",
      "name": "startTime",
      "type": "date",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": "",
        "max": ""
      }
    },
    {
      "id": "endTime",
      "name": "endTime", 
      "type": "date",
      "system": false,
      "required": false,
      "unique": false,
      "options": {
        "min": "",
        "max": ""
      }
    },
    {
      "id": "daysOfWeek",
      "name": "daysOfWeek",
      "type": "json",
      "system": false,
      "required": false,
      "unique": false,
      "options": {}
    },
    {
      "id": "isActive",
      "name": "isActive",
      "type": "bool",
      "system": false,
      "required": false,
      "unique": false,
      "options": {}
    },
    {
      "id": "isRepeating", 
      "name": "isRepeating",
      "type": "bool",
      "system": false,
      "required": false,
      "unique": false,
      "options": {}
    },
    {
      "id": "parameters",
      "name": "parameters",
      "type": "json",
      "system": false,
      "required": false,
      "unique": false,
      "options": {}
    }
  ],
  "indexes": [
    "CREATE INDEX idx_schedules_device ON schedules (deviceId)",
    "CREATE INDEX idx_schedules_active ON schedules (isActive)",
    "CREATE INDEX idx_schedules_type ON schedules (type)"
  ],
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\"", 
  "createRule": "@request.auth.id != \"\"",
  "updateRule": "@request.auth.id != \"\"",
  "deleteRule": "@request.auth.id != \"\"",
  "options": {}
};

// Collection 2: automation_rules
const automationRulesCollection = {
  "id": "automation_rules",
  "name": "automation_rules",
  "type": "base", 
  "system": false,
  "schema": [
    {
      "id": "name",
      "name": "name",
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "description",
      "name": "description", 
      "type": "text",
      "system": false,
      "required": false,
      "unique": false,
      "options": {
        "min": null,
        "max": 1000,
        "pattern": ""
      }
    },
    {
      "id": "deviceId",
      "name": "deviceId",
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "controlId", 
      "name": "controlId",
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "conditions",
      "name": "conditions",
      "type": "json",
      "system": false,
      "required": true,
      "unique": false,
      "options": {}
    },
    {
      "id": "actions",
      "name": "actions",
      "type": "json", 
      "system": false,
      "required": true,
      "unique": false,
      "options": {}
    },
    {
      "id": "isActive",
      "name": "isActive",
      "type": "bool",
      "system": false,
      "required": false,
      "unique": false,
      "options": {}
    },
    {
      "id": "logic",
      "name": "logic",
      "type": "select",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "maxSelect": 1,
        "values": [
          "AND",
          "OR"
        ]
      }
    }
  ],
  "indexes": [
    "CREATE INDEX idx_automation_device ON automation_rules (deviceId)",
    "CREATE INDEX idx_automation_active ON automation_rules (isActive)"
  ],
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\"",
  "createRule": "@request.auth.id != \"\"", 
  "updateRule": "@request.auth.id != \"\"",
  "deleteRule": "@request.auth.id != \"\"",
  "options": {}
};

// Collection 3: schedule_executions
const scheduleExecutionsCollection = {
  "id": "schedule_executions",
  "name": "schedule_executions",
  "type": "base",
  "system": false,
  "schema": [
    {
      "id": "scheduleId",
      "name": "scheduleId",
      "type": "text",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": 1,
        "max": 255,
        "pattern": ""
      }
    },
    {
      "id": "executedAt",
      "name": "executedAt",
      "type": "date",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "min": "",
        "max": ""
      }
    },
    {
      "id": "status",
      "name": "status",
      "type": "select",
      "system": false,
      "required": true,
      "unique": false,
      "options": {
        "maxSelect": 1,
        "values": [
          "success",
          "failed",
          "skipped"
        ]
      }
    },
    {
      "id": "result",
      "name": "result",
      "type": "text",
      "system": false,
      "required": false,
      "unique": false,
      "options": {
        "min": null,
        "max": 2000,
        "pattern": ""
      }
    },
    {
      "id": "error",
      "name": "error",
      "type": "text",
      "system": false,
      "required": false,
      "unique": false,
      "options": {
        "min": null,
        "max": 2000,
        "pattern": ""
      }
    }
  ],
  "indexes": [
    "CREATE INDEX idx_executions_schedule ON schedule_executions (scheduleId)",
    "CREATE INDEX idx_executions_status ON schedule_executions (status)",
    "CREATE INDEX idx_executions_date ON schedule_executions (executedAt)"
  ],
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\"",
  "createRule": "@request.auth.id != \"\"",
  "updateRule": "@request.auth.id != \"\"",
  "deleteRule": "@request.auth.id != \"\"",
  "options": {}
};

// Export collections array for import
const collections = [
  schedulesCollection,
  automationRulesCollection, 
  scheduleExecutionsCollection
];

// For PocketBase Admin UI Import
console.log("Copy this JSON to PocketBase Admin -> Settings -> Import collections:");
console.log(JSON.stringify(collections, null, 2));
