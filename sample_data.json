{"users": [{"id": "user_001", "username": "farmer1", "email": "<EMAIL>", "name": "<PERSON><PERSON>", "emailVisibility": false, "verified": true, "avatar": "", "created": "2024-01-01 08:00:00.000Z", "updated": "2024-01-01 08:00:00.000Z"}, {"id": "user_002", "username": "admin", "email": "<EMAIL>", "name": "Admin Smart Farm", "emailVisibility": false, "verified": true, "avatar": "", "created": "2024-01-01 08:00:00.000Z", "updated": "2024-01-01 08:00:00.000Z"}], "devices": [{"id": "device_001", "deviceName": "Smart Farm Greenhouse A", "deviceId": "device_123", "location": "Greenhouse A - Sektor 1", "isActive": true, "userId": "user_001", "created": "2024-01-01 08:30:00.000Z", "updated": "2024-01-01 08:30:00.000Z"}, {"id": "device_002", "deviceName": "Smart Farm Outdoor B", "deviceId": "device_456", "location": "Outdoor Farm - Sektor 2", "isActive": true, "userId": "user_001", "created": "2024-01-01 09:00:00.000Z", "updated": "2024-01-01 09:00:00.000Z"}], "sensor_data": [{"id": "sensor_001", "deviceId": "device_123", "temperature": 28.5, "humidity": 65.2, "soilMoisture": 45.8, "lightIntensity": 850.0, "ph": 6.8, "created": "2024-01-15 06:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}, {"id": "sensor_002", "deviceId": "device_123", "temperature": 29.1, "humidity": 63.7, "soilMoisture": 42.3, "lightIntensity": 920.0, "ph": 6.9, "created": "2024-01-15 06:05:00.000Z", "updated": "2024-01-15 06:05:00.000Z"}, {"id": "sensor_003", "deviceId": "device_123", "temperature": 30.2, "humidity": 61.5, "soilMoisture": 38.9, "lightIntensity": 1050.0, "ph": 7.0, "created": "2024-01-15 06:10:00.000Z", "updated": "2024-01-15 06:10:00.000Z"}, {"id": "sensor_004", "deviceId": "device_456", "temperature": 26.8, "humidity": 70.1, "soilMoisture": 55.2, "lightIntensity": 780.0, "ph": 6.5, "created": "2024-01-15 06:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}], "controls": [{"id": "control_001", "deviceId": "device_123", "namaKontrol": "Sprinkler System", "idKontrol": 1, "isON": false, "automated": true, "parameter": "Auto mode - Soil moisture < 40%", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}, {"id": "control_002", "deviceId": "device_123", "namaKontrol": "<PERSON><PERSON>", "idKontrol": 2, "isON": true, "automated": false, "parameter": "Manual mode - Speed: Medium", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 05:30:00.000Z"}, {"id": "control_003", "deviceId": "device_123", "namaKontrol": "Drip Irrigation", "idKontrol": 3, "isON": false, "automated": true, "parameter": "Schedule mode - 06:00 & 18:00", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}, {"id": "control_004", "deviceId": "device_123", "namaKontrol": "Mist System", "idKontrol": 4, "isON": false, "automated": true, "parameter": "Auto mode - Humidity < 60%", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}, {"id": "control_005", "deviceId": "device_123", "namaKontrol": "Water Valve", "idKontrol": 5, "isON": false, "automated": false, "parameter": "Manual control", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}, {"id": "control_006", "deviceId": "device_123", "namaKontrol": "Water Pump", "idKontrol": 6, "isON": false, "automated": true, "parameter": "Auto mode - Tank level < 20%", "created": "2024-01-01 10:00:00.000Z", "updated": "2024-01-15 06:00:00.000Z"}], "schedules": [{"id": "schedule_001", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>raman otomatis setiap pagi untuk menjaga kelembaban tanah", "deviceId": "device_123", "controlId": "sprinkler", "type": "time_based", "startTime": "2024-01-15 06:00:00.000Z", "endTime": "2024-01-15 06:30:00.000Z", "daysOfWeek": [1, 2, 3, 4, 5], "isActive": true, "isRepeating": true, "parameters": {"duration": 30, "intensity": "medium", "flow_rate": "5L/min"}, "created": "2024-01-10 08:00:00.000Z", "updated": "2024-01-10 08:00:00.000Z"}, {"id": "schedule_002", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Penyiraman sore hari untuk tanaman yang membutuhkan air ekstra", "deviceId": "device_123", "controlId": "drip", "type": "time_based", "startTime": "2024-01-15 17:00:00.000Z", "endTime": "2024-01-15 17:45:00.000Z", "daysOfWeek": [0, 1, 2, 3, 4, 5, 6], "isActive": true, "isRepeating": true, "parameters": {"duration": 45, "intensity": "low", "flow_rate": "2L/min"}, "created": "2024-01-10 08:15:00.000Z", "updated": "2024-01-10 08:15:00.000Z"}, {"id": "schedule_003", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> angin otomatis saat cuaca panas untuk sirkulasi udara", "deviceId": "device_123", "controlId": "kipas angin", "type": "time_based", "startTime": "2024-01-15 11:00:00.000Z", "endTime": "2024-01-15 15:00:00.000Z", "daysOfWeek": [0, 1, 2, 3, 4, 5, 6], "isActive": true, "isRepeating": true, "parameters": {"duration": 240, "speed": "medium", "auto_adjust": true}, "created": "2024-01-10 08:30:00.000Z", "updated": "2024-01-10 08:30:00.000Z"}, {"id": "schedule_004", "name": "<PERSON><PERSON>", "description": "Sistem kabut untuk meningkatkan kelembaban udara pagi hari", "deviceId": "device_123", "controlId": "mist", "type": "time_based", "startTime": "2024-01-15 07:00:00.000Z", "endTime": "2024-01-15 07:15:00.000Z", "daysOfWeek": [1, 3, 5], "isActive": false, "isRepeating": true, "parameters": {"duration": 15, "intensity": "fine", "humidity_target": 70}, "created": "2024-01-10 08:45:00.000Z", "updated": "2024-01-12 10:00:00.000Z"}], "automation_rules": [{"id": "rule_001", "name": "Auto Sprinkler - <PERSON><PERSON>", "description": "Nyalakan sprinkler otomatis jika kelembaban tanah di bawah 35%", "deviceId": "device_123", "controlId": "sprinkler", "conditions": [{"sensor": "soilMoisture", "operator": "<", "value": 35, "unit": "percent"}], "actions": [{"type": "turn_on", "controlId": "sprinkler", "parameters": {"duration": 20, "intensity": "medium"}}], "isActive": true, "logic": "AND", "created": "2024-01-08 09:00:00.000Z", "updated": "2024-01-08 09:00:00.000Z"}, {"id": "rule_002", "name": "Auto Kipas - <PERSON><PERSON>", "description": "<PERSON>yal<PERSON><PERSON> kipas angin jika suhu lebih dari 32°C", "deviceId": "device_123", "controlId": "kipas angin", "conditions": [{"sensor": "temperature", "operator": ">", "value": 32, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "high", "duration": 60}}], "isActive": true, "logic": "AND", "created": "2024-01-08 09:15:00.000Z", "updated": "2024-01-08 09:15:00.000Z"}, {"id": "rule_003", "name": "Auto Mist - Kelembaban Rendah", "description": "Nyalakan mist system jika kelembaban udara di bawah 55%", "deviceId": "device_123", "controlId": "mist", "conditions": [{"sensor": "humidity", "operator": "<", "value": 55, "unit": "percent"}, {"sensor": "temperature", "operator": ">", "value": 28, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "mist", "parameters": {"duration": 10, "intensity": "fine"}}], "isActive": false, "logic": "AND", "created": "2024-01-08 09:30:00.000Z", "updated": "2024-01-12 14:00:00.000Z"}, {"id": "rule_004", "name": "Emergency Cooling", "description": "Sistem <PERSON>inan darurat jika suhu sangat tinggi", "deviceId": "device_123", "controlId": "kipas angin", "conditions": [{"sensor": "temperature", "operator": ">", "value": 38, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "maximum", "duration": 120}}, {"type": "turn_on", "controlId": "mist", "parameters": {"duration": 30, "intensity": "heavy"}}], "isActive": true, "logic": "AND", "created": "2024-01-08 09:45:00.000Z", "updated": "2024-01-08 09:45:00.000Z"}], "schedule_executions": [{"id": "exec_001", "scheduleId": "schedule_001", "executedAt": "2024-01-15 06:00:00.000Z", "status": "success", "result": "Sprinkler activated successfully for 30 minutes. Soil moisture increased from 38% to 52%", "error": "", "created": "2024-01-15 06:00:00.000Z", "updated": "2024-01-15 06:30:00.000Z"}, {"id": "exec_002", "scheduleId": "schedule_002", "executedAt": "2024-01-14 17:00:00.000Z", "status": "success", "result": "Drip irrigation completed. Duration: 45 minutes. Water usage: 90L", "error": "", "created": "2024-01-14 17:00:00.000Z", "updated": "2024-01-14 17:45:00.000Z"}, {"id": "exec_003", "scheduleId": "schedule_003", "executedAt": "2024-01-14 11:00:00.000Z", "status": "success", "result": "Ventilation system ran for 4 hours. Temperature maintained at 29-31°C", "error": "", "created": "2024-01-14 11:00:00.000Z", "updated": "2024-01-14 15:00:00.000Z"}, {"id": "exec_004", "scheduleId": "schedule_001", "executedAt": "2024-01-14 06:00:00.000Z", "status": "failed", "result": "", "error": "Water pump malfunction detected. Unable to start sprinkler system", "created": "2024-01-14 06:00:00.000Z", "updated": "2024-01-14 06:01:00.000Z"}, {"id": "exec_005", "scheduleId": "schedule_004", "executedAt": "2024-01-13 07:00:00.000Z", "status": "skipped", "result": "Schedule skipped - humidity already at target level (72%)", "error": "", "created": "2024-01-13 07:00:00.000Z", "updated": "2024-01-13 07:00:00.000Z"}, {"id": "exec_006", "scheduleId": "schedule_002", "executedAt": "2024-01-13 17:00:00.000Z", "status": "success", "result": "Drip irrigation completed successfully. All zones watered", "error": "", "created": "2024-01-13 17:00:00.000Z", "updated": "2024-01-13 17:45:00.000Z"}]}