# PocketBase Collections Setup untuk Smart Farming Scheduling

## 📋 Overview
File ini berisi instruksi lengkap untuk setup collection PocketBase yang dibutuhkan untuk fitur scheduling pada aplikasi Smart Farming.

## 🗂️ Collections yang Akan Dibuat

### 1. **schedules** - <PERSON>yimpan jadwal otomatis
- <PERSON><PERSON>wal berdasarkan waktu (time-based)
- Jadwal berdasarkan sensor (sensor-based) 
- Jadwal manual

### 2. **automation_rules** - Menyimpan aturan otomasi
- Kondisi sensor (suhu, kelembaban, dll)
- <PERSON><PERSON><PERSON> yang akan dijalankan
- Logic AND/OR untuk multiple conditions

### 3. **schedule_executions** - Log eksekusi jadwal
- History kapan jadwal dijalankan
- Status sukses/gagal
- Error logging

## 🚀 Cara Setup (Metode 1: Import JSON)

### Step 1: Buka PocketBase Admin
1. Akses PocketBase Admin UI di: `http://**************:8080/_/`
2. Login dengan akun admin

### Step 2: Import Collections
1. Klik **Settings** di sidebar kiri
2. Pilih **Import collections**
3. Copy seluruh isi file `pocketbase_collections.json`
4. Paste ke text area
5. Klik **Review** untuk preview
6. Klik **Import** untuk membuat collections

## 🔧 Cara Setup (Metode 2: Manual)

Jika import gagal, buat manual dengan struktur berikut:

### Collection: schedules
```
Fields:
- name (text, required, max: 255)
- description (text, optional, max: 1000)  
- deviceId (text, required, max: 255)
- controlId (text, required, max: 255)
- type (select: time_based, sensor_based, manual)
- startTime (date, required)
- endTime (date, optional)
- daysOfWeek (json, optional)
- isActive (bool, default: false)
- isRepeating (bool, default: false)
- parameters (json, optional)

API Rules:
- List: @request.auth.id != ""
- View: @request.auth.id != ""
- Create: @request.auth.id != ""
- Update: @request.auth.id != ""
- Delete: @request.auth.id != ""
```

### Collection: automation_rules
```
Fields:
- name (text, required, max: 255)
- description (text, optional, max: 1000)
- deviceId (text, required, max: 255)
- controlId (text, required, max: 255)
- conditions (json, required)
- actions (json, required)
- isActive (bool, default: false)
- logic (select: AND, OR)

API Rules: (sama seperti schedules)
```

### Collection: schedule_executions
```
Fields:
- scheduleId (text, required, max: 255)
- executedAt (date, required)
- status (select: success, failed, skipped)
- result (text, optional, max: 2000)
- error (text, optional, max: 2000)

API Rules: (sama seperti schedules)
```

## 📊 Contoh Data

### Sample Schedule (time-based)
```json
{
  "name": "Penyiraman Pagi",
  "description": "Penyiraman otomatis setiap pagi jam 6",
  "deviceId": "device_123",
  "controlId": "sprinkler",
  "type": "time_based",
  "startTime": "2024-01-01T06:00:00Z",
  "endTime": "2024-01-01T06:30:00Z",
  "daysOfWeek": [1, 2, 3, 4, 5],
  "isActive": true,
  "isRepeating": true,
  "parameters": {
    "duration": 30,
    "intensity": "medium"
  }
}
```

### Sample Automation Rule (sensor-based)
```json
{
  "name": "Auto Kipas Suhu Tinggi",
  "description": "Nyalakan kipas jika suhu > 35°C",
  "deviceId": "device_123", 
  "controlId": "kipas angin",
  "conditions": [
    {
      "sensor": "temperature",
      "operator": ">",
      "value": 35,
      "unit": "celsius"
    }
  ],
  "actions": [
    {
      "type": "turn_on",
      "controlId": "kipas angin",
      "parameters": {
        "speed": "high"
      }
    }
  ],
  "isActive": true,
  "logic": "AND"
}
```

## ✅ Verifikasi Setup

Setelah setup selesai, pastikan:

1. **Collections terbuat**: Cek di PocketBase Admin -> Collections
2. **API endpoints aktif**: 
   - `GET /api/collections/schedules/records`
   - `GET /api/collections/automation_rules/records`
   - `GET /api/collections/schedule_executions/records`

3. **Test dengan Flutter app**: Jalankan `SchedulingScreen` untuk memastikan API berfungsi

## 🔍 Troubleshooting

### Error: "Collection already exists"
- Hapus collection yang sudah ada terlebih dahulu
- Atau skip collection yang sudah ada

### Error: "Invalid field type"
- Pastikan menggunakan PocketBase versi terbaru
- Cek format JSON tidak ada syntax error

### Error: "Authentication required"
- Pastikan API rules sudah diset dengan benar
- Test dengan user yang sudah login

## 📱 Integrasi dengan Flutter

Setelah collections dibuat, aplikasi Flutter sudah siap menggunakan:

1. **ScheduleApi** - untuk CRUD schedules
2. **CreateScheduleScreen** - untuk membuat jadwal baru
3. **SchedulingScreen** - untuk melihat daftar jadwal

## 🔄 Update URL Endpoints

Pastikan file `lib/common/url.dart` sudah include endpoint baru:

```dart
final String url_schedules = 'http://**************:8080/api/collections/schedules';
final String url_automation_rules = 'http://**************:8080/api/collections/automation_rules';
final String url_schedule_executions = 'http://**************:8080/api/collections/schedule_executions';
```

## 🎯 Next Steps

1. Setup collections di PocketBase ✅
2. Test API endpoints dengan Postman/curl
3. Test Flutter app scheduling features
4. Implement background scheduler service
5. Add push notifications untuk schedule alerts

---

**📞 Support**: Jika ada masalah, pastikan PocketBase server berjalan dan accessible di `http://**************:8080`
